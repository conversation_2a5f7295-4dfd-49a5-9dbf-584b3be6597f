#ifndef __UniTextUtility_h__
#define __UniTextUtility_h__

#include "Common/UniPlatform.h"
#include "Common/Unicode/Unicode.h"

#include "Allocator.h"

#include <memory>
#include <string>

NAMESPACE_BEGIN

/// <summary>
/// String Utility
/// </summary>

// a very simple memory pool
template<typename T>
class SharedMemPool
{
public:
	~SharedMemPool() noexcept
	{
		if (m_Buffer != nullptr) delete[] m_Buffer;
		m_Buffer = nullptr;
		m_Size = 0;
	}
	[[nodiscard]] T* Allocate(size_t size)
	{
		if (m_Size < size)
		{
			if (m_Buffer != nullptr)
			{
				delete[] m_Buffer;
				m_Buffer = nullptr;
			}
			m_Size = size;
			m_Buffer = new T[m_Size * sizeof(T)];
		}

		return m_Buffer;
	}

private:
	T* m_Buffer{ nullptr };
	size_t m_Size{ 0 };
};

/// <summary>
/// (Old comments, now this is implemented using memory pool)
/// This is a very bad implementation: 
/// 1. memory is always re-allocated on heap!
/// </summary>
struct UTF16String 
{
	// 8 * 1000 * sizeof(uint16) < 16kb on stack
	// exceeds this value will cause C6262 warning
	// more details at: https://learn.microsoft.com/en-us/cpp/code-quality/c6262?view=msvc-170
	// Personally I think it is safe to use less than 32kb on the stack, which is 16 * 1024 * sizeof(uint16);
	static const int kStackMemorySize = 8 * 1000;

	UTF16String() : data(nullptr), length(0) {}

	UTF16String(const char* str)
	{
		size_t len = strlen(str);
		if (len > 0)
		{
			allocate(len);
			ConvertUTF8toUTF16(str, len, data, length);
		}
		else
		{
			length = 0;
			data = nullptr;
		}
	}

	UTF16String(const UTF16String& other)
	{
		if (other.length != 0)
		{
			length = other.length;
			allocate(length);
			memcpy(data, other.data, sizeof(uint16) * length);
		}
		else 
		{
			length = 0;
			data = nullptr;
		}
	}

	UTF16String& operator=(const char* str) 
	{
		assign(str, strlen(str));
		return *this;
	}

	UTF16String& operator=(const UTF16String& other)
	{
		if (other.length == 0 || other.data == nullptr)
		{
			length = 0;
			data = nullptr;
		}
		else
		{
			length = other.length;
			allocate(length);
			if (data != nullptr) memcpy(data, other.data, sizeof(uint16) * length);
		}
		return *this;
	}

	UTF16String& assign(const char* str, size_t len)
	{
		if (len > 0)
		{
			allocate(len);
			ConvertUTF8toUTF16(str, len, data, length);
		}
		else
		{
			length = 0;
			data = nullptr;
		}		
		return *this;
	}

	~UTF16String()
	{
		data = nullptr;
		length = 0;
	}

	static SharedMemPool<uint16> s_MemPool;
	void allocate(size_t len)
	{
		if (len < kStackMemorySize) 
		{
			data = &m_StackBuffer[0];
		}
		else
		{
			data = s_MemPool.Allocate(len);
		}
		length = static_cast<int>(len);
	}

	uint16 operator[](int index) const { return data[index]; }

	friend bool operator==(const UTF16String& lhs, const UTF16String& rhs)
	{
		if (lhs.length != rhs.length) return false;

		return (rhs.data == nullptr || memcmp(lhs.data, rhs.data, lhs.length * sizeof(uint16)) == 0);
	}

	uint16* data;
	int length;

private:
	// stack memory
	std::array<uint16, kStackMemorySize> m_StackBuffer{0};
};

// String types for UniText - keeping them together for better organization
using UniStringView = std::basic_string_view<uint16>;
static inline const UniStringView UniStringViewEmpty = UniStringView(nullptr, 0);
using UniString = std::basic_string<char, std::char_traits<char>, std::pmr::polymorphic_allocator<char>>;

template <int First, int Last>
struct static_for
{
	template <typename Fn>
	void operator()(Fn const& fn) const
	{
		if constexpr (First < Last)
		{
			fn(First);
			static_for<First + 1, Last>()(fn);
		}
	}
};

template <int N>
struct static_for<N, N>
{
	template <typename Fn>
	void operator()(Fn const& fn) const
	{ }
};

/// <summary>
/// Font Size Rounding Utilities
/// </summary>

// each step, we grow by 8
constexpr int kFontSizeStep = 8;
constexpr int kFontSizeHalfStep = kFontSizeStep >> 1;
constexpr int RoundFontSize(int fontSize, int minFontSize, int maxFontSize) noexcept
{
	if (fontSize <= minFontSize) return minFontSize;
	if (fontSize >= maxFontSize) return maxFontSize;

	for (int sizeInPage = minFontSize; sizeInPage <= maxFontSize; sizeInPage += kFontSizeStep)
	{
		if (abs(fontSize - sizeInPage) <= kFontSizeHalfStep)
		{
			return sizeInPage;
		}
	}

	// Not found!! This shouldn't happen!
	return maxFontSize;
}

constexpr int RoundFontSizeIndex(int fontSize, int minFontSize, int maxFontSize) noexcept
{
	if (fontSize <= minFontSize) return 0;
	
	int index = 0;
	for (int sizeInPage = minFontSize; sizeInPage <= maxFontSize; sizeInPage += kFontSizeStep, index++)
	{
		if (abs(fontSize - sizeInPage) <= kFontSizeHalfStep)
		{
			break;
		}
	}
	return index;
}
NAMESPACE_END

// Hash specialization for UniStringView to enable use in std::unordered_map
namespace std {
    template<>
    struct hash<UniText::UniStringView> {
        std::size_t operator()(const UniText::UniStringView& sv) const noexcept {
            std::size_t hash_value = 0;
            for (auto c : sv) {
                hash_value = hash_value * 31 + static_cast<std::size_t>(c);
            }
            return hash_value;
        }
    };
}

#endif // __UniTextUtility_h__