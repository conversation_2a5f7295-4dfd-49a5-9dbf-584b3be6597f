Hello! I've been developing a native text rendering plugin for unity mobile games in the past 2 years. This series will contain contents like: dynamic sdf font rendering, atlas management, integrate with Unity, and GPU sdf support! Also will touch a little bit about Graphics API Abstraction(mostly on how I did it). 

As a start, Part 1 of the series will start with the background: Why I do this in the first place?

## Who am I? 

I'm a game developer with over decade of experience in the industry. I've been working on a global released mobile game for past 7 years using Unity. I could say I'm very familiar with both Unity UI's built-in Text component and the not so new kid: TextMeshPro.

## What is the Problem?

Neither Text nor TextMeshPro is satisfying in terms of performance and features. So we ended up using both.

### Text

  ✅Pros:
  - fully dynamic at runtime.
  - fallback to system fonts.
  
  ❌Cons:
  - poor performance when triggering texture rebuilt.
  - poor performance when it comes to shadow and outline effects. (based on vertices).
  - no support for text shaping. (like Arabic, Hebrew, etc.)
  - no right-to-left support.

  Usage: where dynamic(UGC:user generated content) texts are needed.
  - In-game chat.
  - User Nickname and more.

### TextMeshPro

  ✅Pros:
  - SDF support, making effects like shadow and outline much easier and performant.
  - Right-to-left support.
  - Static fonts are generally better in performance.

  ❌Cons:
  - No fallback to system fonts. (need to have all fonts baked in)
  - More memory usage. (depending on both glyph number and font size when generating the static font)
  - High cost when it comes to multi-language support: for each original .ttf or .otf font, corresponding static font need to be generated. We were able to automate the process, but it's still a huge cost.
  - Use only ONE font size when generating the static font, that makes it unable to display texts with high quality in large size.(You can't render 72 font size from a 24 font size static font)
  
  (TODO: Put pictures here to demonstrate the problem).

  Usage: where texts are localized and can be pre-baked, that makes most scenarios in the game.

## What is the solution?

After working with both Text and TextMeshPro for quite some time, I came to the conclusion that neither of them is satisfying in terms of performance and features. So I decided to build my own text rendering plugin. And the answer became pretty clear: SDF + Dynamic Font Rendering.

### SDF

The idea of using signed-distance-field for text rendering can be found in [Vavle's paper](https://steamcdn-a.akamaihd.net/apps/valve/2007/SIGGRAPH2007_AlphaTestedMagnification.pdf). 

(TODO: Put pictures here to demonstrate SDF)

Let's summarize the process in 3 steps:

1. Upscale the font size to a larger size and render the glyph into a texture with 4096x4096 resolution. (Depending on the scale factor, we have SDF16, SDF32, etc.)
2. Calculate the distance from each pixel to the nearest edge pixel.
3. Downscale the calculated texture to the desired size.

The result is a texture that contains the distance information. Then we can use this texture to render text at any size(within a reasonable range).

But the process is quite time consuming because of the size of the texture. So back then the font atlas can only be pre-baked offline. Until someone came up with a better solution: [Anti-Aliased Euclidean Distance Transform](TODO:Put Link Here)(AAEDT for short).

(TODO: Put pictures here to demonstrate AAEDT)

The idea is to calculate the distance field in a more efficient way, by using the alpha value of the glyph as a hint to the distance. This way we can achieve the same result with a much smaller texture size. And the best part is, we can do it in real-time! For more details on how this method works, please refer to the paper!

And fortunately, AAEDT is already implemented within FreeType 2.11.0 and newer!

### Benchmark



## Conclusion

With AAEDT, we can now render text with signed distance field in real-time. Since it is already implemented within FreeType, we should be able to integrate it with Unity's NativeRenderPlugin easily.

In the coming articles, I will go into details about how this can be achieved using native c++ with FreeType and UnityNativeRenderPlugin.
