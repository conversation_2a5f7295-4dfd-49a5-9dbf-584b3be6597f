﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

#if UNITY_EDITOR
using UnityEditor;
#endif

[ExecuteAlways]
[RequireComponent(typeof(CanvasRenderer))]
public class UniText_UGUI : MaskableGraphic
{
#if UNITY_EDITOR
    /// <summary>
    /// 编辑器下，看效果的文本，不序列化，全部走运行时文本
    /// </summary>
    [SerializeField]
    private string m_TextInEditor;

    protected override void OnValidate()
    {
        if (Application.isPlaying) return;
        if (EditorApplication.isPlayingOrWillChangePlaymode) return;

        base.OnValidate();

        text = m_TextInEditor;

        //m_FontPath = "Assets/Fonts/PFAgoraSlabPro Bold.ttf"; 
        // if (m_UniText != null) m_UniText.fontPath = m_FontPath; 
        if (m_UniText != null)
        {
            m_UniText.fontSize = m_FontSize;
            m_UniText.horizontalAlignment = m_HorizontalAlignment;
            m_UniText.verticalAlignment = m_VerticalAlignment;
            m_UniText.horizontalOverflow = m_HorizontalOverflow;
            m_UniText.verticalOverflow = m_VerticalOverflow;
            m_UniText.enableRTL = m_EnableRTL;
            m_UniText.enableKerning = m_EnableKerning;
            m_UniText.enableRichText = m_EnableRichText;
            m_UniText.enableAutoSize = m_EnableAutoSize;
        }
        SetMaterialDirty(); 
        SetVerticesDirty();
    }
#endif

    public string text 
    { 
        get { return m_UniText.text; } 
        set
        {
            uniText.text = value;
            SetAllDirty();
        } 
    }
    /*
    [SerializeField]
    private string m_FontPath;
    public string fontPath 
    { 
        get { return m_FontPath; } 
        set 
        { 
            if (value == m_FontPath) return; 
            m_FontPath = value; //uniText.fontPath = value;
            SetMaterialDirty();
        }
    }
    */

    [SerializeField]
    private bool m_EnableRTL = false;
    public bool enableRTL
    {
        get { return m_EnableRTL; }
        set
        {
            if (value == m_EnableRTL) return;
            m_EnableRTL = value; uniText.enableRTL = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private bool m_EnableKerning = true;
    public bool enableKerning
    {
        get { return m_EnableKerning; }
        set
        {
            if (value == m_EnableKerning) return;
            m_EnableKerning = value; uniText.enableKerning = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private bool m_EnableRichText = true;
    public bool enableRichText
    {
        get { return m_EnableRichText; }
        set
        {
            if (value == m_EnableRichText) return;
            m_EnableRichText = value; uniText.enableRichText = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private bool m_EnableAutoSize = false;
    public bool enableAutoSize
    {
        get { return m_EnableAutoSize; }
        set
        {
            if (value == m_EnableAutoSize) return;
            m_EnableAutoSize = value; uniText.enableAutoSize = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    protected UniFont m_Font;
    public UniFont font
    {
        get { return m_Font; }
        set
        {
            if (m_Font != value)
            {
                if (m_Font != null) m_Font.UnregisterUniText(this);
                m_Font = value;
                if (m_Font != null) m_Font.RegisterUniText(this);
                SetAllDirty();
                //isFontDirty = true;
            }
        }
    }

    [SerializeField]
    private int m_FontSize = 24;
    public int fontSize
    {
        get { return m_FontSize; }
        set 
        {
            if (value == m_FontSize) return;
            m_FontSize = value; uniText.fontSize = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private HorizontalAlignment m_HorizontalAlignment = HorizontalAlignment.Left;
    public HorizontalAlignment horizontalAlignment
    {
        get { return m_HorizontalAlignment; }
        set
        {
            if (value == m_HorizontalAlignment) return;
            m_HorizontalAlignment = value; uniText.horizontalAlignment = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private VerticalAlignment m_VerticalAlignment = VerticalAlignment.Middle;
    public VerticalAlignment verticalAlignment
    {
        get { return m_VerticalAlignment; }
        set
        {
            if (value == m_VerticalAlignment) return;
            m_VerticalAlignment = value; uniText.verticalAlignment = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private HorizontalOverflow m_HorizontalOverflow = HorizontalOverflow.Wrap;
    public HorizontalOverflow horizontalOverflow
    {
        get { return m_HorizontalOverflow; }
        set
        {
            if (value == m_HorizontalOverflow) return;
            m_HorizontalOverflow = value; uniText.horizontalOverflow = value; SetVerticesDirty();
        }
    }

    [SerializeField]
    private VerticalOverflow m_VerticalOverflow = VerticalOverflow.Truncate;
    public VerticalOverflow verticalOverflow
    {
        get { return m_VerticalOverflow; }
        set
        {
            if (value == m_VerticalOverflow) return;
            m_VerticalOverflow = value; uniText.verticalOverflow = value; SetVerticesDirty();
        }
    }

    public float pixelsPerUnit
    {
        get
        {
            var localCanvas = canvas;
            if (!localCanvas)
                return 1;

            return localCanvas.scaleFactor;
        }
    }

    protected UniTextBridge m_UniText;
    public UniTextBridge uniText { get { if (m_UniText == null) m_UniText = new UniTextBridge(); return m_UniText; } }

    /// <summary>
    /// Text's texture comes from the font.
    /// </summary>
    public override Texture mainTexture
    {
        get
        {
            /*
            if (font != null && font.material != null && font.material.mainTexture != null)
                return font.material.mainTexture;
            */
            //Debug.Log("Access to Main Texture: " + m_Textures);
            if (UniUtilities.s_Textures != null && UniUtilities.s_Textures.Length > 0)
            {
                return UniUtilities.s_Textures[0];
            }

            if (m_Material != null)
                return m_Material.mainTexture;
            

            return base.mainTexture;
        }
    }

    protected override void Awake()
    {
        base.Awake();
        useLegacyMeshGeneration = false;
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        if (m_Font != null) m_Font.RegisterUniText(this);

        uniText.OnEnable();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        if (m_Font != null) m_Font.UnregisterUniText(this);

        if (m_UniText != null) m_UniText.OnDisable();
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        var r = GetPixelAdjustedRect();
        var scaleFactor = pixelsPerUnit;
        uniText.SetBounds(r.width * scaleFactor, r.height * scaleFactor);

        // temp fix
        //uniText.fontPath = m_FontPath;
        //m_UniText.font
        // also a temp fix
        uniText.font = m_Font;
        uniText.OnUpdateText(toFill);
    }

    protected override void UpdateMaterial()
    {
        if (!IsActive())
            return;

        // temp fix
        //uniText.fontPath = m_FontPath;

        var mat = materialForRendering;
        //Debug.Log("Update Material: " + m_UniText);
        // Set material textures
        int textureCount = UniUtilities.s_Textures != null ? UniUtilities.s_Textures.Length : 0;

        var _textureIDs = UniUtilities.s_TextureNameID;
        for (int i = 0; i < textureCount; i++)
        {
            if (mat.HasProperty(_textureIDs[i]))
            {
                mat.SetTexture(_textureIDs[i], UniUtilities.s_Textures[i]);
            }
        }

        canvasRenderer.materialCount = 1;
        canvasRenderer.SetMaterial(mat, 0);
    }
}
