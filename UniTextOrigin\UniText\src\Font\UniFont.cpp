
#include "Common/UniMacro.h"
#include "UniFont.h"
#include "UniFontFallback.h"
#include "UniTextGlobal.h"
#include "IUniFontImpl.h"

#include "RenderAPI.h"

#include <array>
#include <string_view>

#if USE_FREETYPE
#include "FreeType/UniFontFreeType.h"
#endif

#include "Custom/UniCustomFont.h"

NAMESPACE_USE

//////////////////////////////////////////////
/// statics & consts & globals
// Note: s_SystemFallbackFonts removed - now using UniFontFallbackCache directly

#if USE_THREAD_POOL
#include "../Utility/BS_thread_pool_light.hpp"

static BS::thread_pool_light s_ThreadPool(kThreadNum);
static std::array<std::mutex, kThreadNum> s_Mutex;
static std::mutex g_Mutex;
#endif

#if 0
static unsigned char s_FTLibraryRef = 0;
static FT_Library s_FTLibrary = NULL;

constexpr float kShiftFactor = static_cast<float>(1 << 6);
constexpr float kInvOfShiftFactor = 1.f / kShiftFactor;

constexpr int SDFSpreadSize(int fontSize)
{
    constexpr int minSpread = 2;    // for fontSize <= 12
    constexpr int maxSpread = 10;   // for fontSize >= 90
    if (fontSize <= 12) return minSpread;
    if (fontSize >= 90) return maxSpread;

    return minSpread + 8 * (fontSize - 12) / 78;
}
#endif

// This function is now provided by the FontSettings class in UniFontSettings.h

std::unique_ptr<UniFont> UniFont::CreateFromFile(const char* fontPath)
{
    auto font = std::make_unique<UniFont>(fontPath);
    if (font->InitLibrary())
        font->CreateFace(fontPath);

    return font;
}

std::unique_ptr<UniFont> UniFont::CreateFromMemory(const char* fontName, const unsigned char* fileBuffer, unsigned long bufferSize)
{
    auto font = std::make_unique<UniFont>(fontName);
    if (font->InitLibrary())
        font->CreateMemoryFace(fileBuffer, bufferSize);

    return font;
}

void UniFont::InitializeSystemFonts()
{
    // Initialize the font fallback manager
    auto fallbackManager = UniTextGlobal::GetFontFallbackCache();
    if (!fallbackManager->Initialize())
    {
        return;
    }

    // Note: System fallback fonts are now managed entirely by UniFontFallbackCache
    // No need to pre-load or cache them in a static vector
}

void UniFont::DestroySystemFonts()
{
    // Note: System fallback fonts are now managed entirely by UniFontFallbackCache
    // No cleanup needed here as fonts are managed by UniFontCache
}

void UniFont::UpdateAllFontsIfSettingsChanged()
{
    // Check if settings have changed
    if (UniText::UniTextGlobal::GetSettings().HasChanged())
    {
        // Clear all font caches
        auto fontCache = UniTextGlobal::GetFontCache();
        fontCache->ForeachFont([](UniFont* font) {
            if (font != nullptr)
            {
                font->ClearGlyphs();
            }
        });
    }
}

//////////////////////////////////////////////
/// publics
//////////////////////////////////////////////
UniFont::UniFont(const char* fontName, std::unique_ptr<IUniFontImpl> impl)
    : UniObject(fontName)
{
    if (!impl)
    {
#if USE_FREETYPE
        // Create default FreeType implementation
        impl = std::make_unique<UniFontFreeType>();
#else
        // Create default custom implementation when FreeType is disabled
        impl = std::make_unique<UniCustomFont>();
#endif
    }
    m_pImpl = std::move(impl);

    // Initialize font properties with defaults
    // m_FontProperties.minFontSize = 12;
    // m_FontProperties.maxFontSize = 72;
    // m_FontProperties.padding = 1;
    // m_FontProperties.renderMode = RenderMode::SmoothHinted;
    // m_FontProperties.packingMethod = PackingMethod::MaxRects;
    // m_FontProperties.defaultTextureWidth = 1024;
    // m_FontProperties.defaultTextureHeight = 1024;
    // m_FontProperties.allowRotation = false;
    // m_AtlasEntry = std::make_unique<UniFontAtlasEntry>(m_FontProperties.minFontSize, m_FontProperties.maxFontSize);

    // Setup using defaults
    Setup(FontProperties{});
}

UniFont::~UniFont() noexcept
{
    // Clear all glyphs and release resources
    ClearGlyphs();

    // Clear fallback fonts
    m_FallbackFonts.clear();
}

bool UniFont::InitLibrary()
{
    if (m_pImpl)
    {
        return m_pImpl->InitLibrary();
    }
    return false;
}

bool UniFont::CreateFace(const char* filePath)
{
    if (m_pImpl)
    {
        return m_pImpl->CreateFace(filePath);
    }
    return false;
}

bool UniFont::CreateMemoryFace(const unsigned char* fileBytes, unsigned long fileSize)
{
    if (m_pImpl)
    {
        return m_pImpl->CreateMemoryFace(fileBytes, fileSize);
    }
    return false;
}

void UniFont::Setup(const FontProperties& props)
{
    m_FontProperties.packingMethod          = props.packingMethod;
    //m_FontProperties.packingMode            = props.packingMode;
    m_FontProperties.renderMode             = props.renderMode;
    m_FontProperties.minFontSize            = props.minFontSize;
    m_FontProperties.maxFontSize            = props.maxFontSize;
    m_FontProperties.padding                = props.padding;
    m_FontProperties.defaultTextureWidth    = props.defaultTextureWidth;
    m_FontProperties.defaultTextureHeight   = props.defaultTextureHeight;
    m_FontProperties.allowRotation          = props.allowRotation;
    // Reset the atlas entry with the new font size range
    if (m_AtlasEntry)
    {
        m_AtlasEntry->Clear();
    }
    
    m_AtlasEntry = std::make_unique<UniFontAtlasEntry>();
    m_AtlasEntry->OnFontSizeChanged(props.minFontSize, props.maxFontSize);
}

int UniFont::GetFontGUID() const
{
    return GetGUID();
}

void UniFont::SetPackingMethod(PackingMethod packingMethod) noexcept
{
    m_FontProperties.packingMethod = packingMethod;
}

void UniFont::SetPadding(uint8 padding) noexcept
{
    m_FontProperties.padding = padding;
}

void UniFont::SetRenderMode(RenderMode renderMode) noexcept
{
    m_FontProperties.renderMode = renderMode;
}

void UniFont::AddFallbackFont(const char* fontName)
{
    auto font_ptr = UniTextGlobal::GetFontCache()->GetFont(fontName);
    if (font_ptr != nullptr)
    {
        m_FallbackFonts.push_back(font_ptr);
    }
}

float UniFont::GetKerning(uint16 first, uint16 second) const noexcept
{
    if (m_pImpl)
    {
        return m_pImpl->GetKerning(first, second);
    }
    return 0.0f;
}

float UniFont::GetAscender(int fontSize) const noexcept
{
    if (m_pImpl)
    {
        return m_pImpl->GetAscender(fontSize);
    }
    return 0.0f;
}

float UniFont::GetDescender(int fontSize) const noexcept
{
    if (m_pImpl)
    {
        return m_pImpl->GetDescender(fontSize);
    }
    return 0.0f;
}

float UniFont::GetLineSpacing(int fontSize) const noexcept
{
    if (m_pImpl)
    {
        return m_pImpl->GetLineSpacing(fontSize);
    }
    return 0.0f;
}

uint8 UniFont::GetPadding() const noexcept
{
    return m_FontProperties.padding;
}

RenderMode UniFont::GetRenderMode() const noexcept
{
    return m_FontProperties.renderMode;
}

int UniFont::GetMinFontSize() const noexcept
{
    return m_FontProperties.minFontSize;
}

int UniFont::GetMaxFontSize() const noexcept
{
    return m_FontProperties.maxFontSize;
}

uint32 UniFont::GetCharIndex(const uint16 c, uint32 thread_id) const
{
    if (!m_pImpl)
        return 0;

    return m_pImpl->GetCharIndex(c, thread_id);
}

const std::string& UniFont::GetFamilyName() const
{
    if (m_pImpl)
    {
        return m_pImpl->GetFamilyName();
    }

    // Return a static empty string for null implementation
    static const std::string empty_string;
    return empty_string;
}

FontStyle UniFont::GetFontStyle() const
{
    if (m_pImpl)
    {
        return m_pImpl->GetFontStyle();
    }
    return FontStyle::Regular;
}

void UniFont::SetFontSizeRange(int minFontSize, int maxFontSize) noexcept
{
    m_FontProperties.minFontSize = minFontSize;
    m_FontProperties.maxFontSize = maxFontSize;
}

void UniFont::SetFontSizeForRendering(int fontSize)
{
    if (m_pImpl)
    {
        m_pImpl->SetFontSizeForRendering(fontSize, m_FontProperties.renderMode);
    }
}
/*
const SizedPage* UniFont::GetGlyphPage(uint16 unicode, int fontSize) const noexcept
{
    if (!m_AtlasEntry)
        return nullptr;

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(fontSize);
    if (!lookUpTable)
        return nullptr;

    GlyphInfo* info = nullptr;
    if (!lookUpTable->FindGlyphInfo(unicode, &info))
        return nullptr;

    if (info->pageIndex < 0 || info->pageIndex >= lookUpTable->pageList.size())
        return nullptr;

    return &lookUpTable->pageList[info->pageIndex];
}
*/
const SizedPage* UniFont::GetGlyphPage(int fontSize, const GlyphInfo& glyph) const noexcept
{
    if (!m_AtlasEntry)
        return nullptr;

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(fontSize);
    if (!lookUpTable)
        return nullptr;

    if (glyph.pageIndex < 0 || glyph.pageIndex >= lookUpTable->pageList.size())
        return nullptr;

    return &lookUpTable->pageList[glyph.pageIndex];
}

const ITexture* UniFont::GetFontAtlas(int fontSize, int index) const
{
    if (!m_AtlasEntry)
        return nullptr;

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(fontSize);
    if (!lookUpTable)
        return nullptr;

    if (index < 0 || index >= lookUpTable->pageList.size())
        return nullptr;

    return lookUpTable->pageList[index].textureEntry.GetTexture();
}

const IUniGlyphPacker* UniFont::GetAtlasPacker(int fontSize, int index) const
{
    if (!m_AtlasEntry)
        return nullptr;

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(fontSize);
    if (!lookUpTable)
        return nullptr;

    if (index < 0 || index >= lookUpTable->pageList.size())
        return nullptr;

    return lookUpTable->pageList[index].glyphPacker.get();
}

#if 0
FT_Int32 GetLoadFlags(RenderMode renderMode)
{
    FT_Int32 loadFlags = FT_LOAD_DEFAULT;
    switch (renderMode)
    {
    case RenderMode::Smooth:
        loadFlags = FT_LOAD_TARGET_NORMAL | FT_LOAD_NO_HINTING;
        break;
    case RenderMode::SmoothHinted:
        loadFlags = FT_LOAD_TARGET_NORMAL;
        break;
    case RenderMode::Raster:
        loadFlags = FT_LOAD_TARGET_MONO;
        break;
    case RenderMode::SDF:
    case RenderMode::BSDF:
        loadFlags = FT_LOAD_NO_HINTING;
        break;
    default:
        loadFlags = FT_LOAD_TARGET_NORMAL | FT_LOAD_NO_HINTING;
        break;
    }

    return loadFlags;
}

/// <summary>
/// 逻辑最复杂的函数，可能是这个了，后续可以考虑把每个Step拆分成独立inline函数
/// </summary>
template<bool renderGlyph>
bool UniFont::LoadGlyph(const uint16 c, int fontSize, GlyphInfo* out)
{
    bool isGlyphCached = false;
    int roundedSize = RoundFontSize(fontSize);

    // Step 1: Get from cache
    if (m_GlyphLookupMap.size() > 0)
    {
        auto entry_it = m_GlyphLookupMap.find(GlyphEntry{ c, roundedSize });
        if (entry_it != m_GlyphLookupMap.end())
        {
            decltype(auto) page = m_AtlasEntry->GetGlyphPage(roundedSize, entry_it->second);
            if (page != nullptr)
            {
                // find and size did not change
                decltype(auto) ret = page->glyphs->find(c);
                if (ret != page->glyphs->end())
                {
                    ret->second.usedCount++;
                    *out = ret->second;
                    return true;
                }
            }
            else
            {
                isGlyphCached = true;
                // requires a larger fontSize
                //m_GlyphLookupMap.erase(c);
                //page->erase(c);
            }
        }
    }

    // Step 2: Get face
    FT_Face face = m_FTFace;
    if (nullptr == face) return false;

    FT_Error error;
    FT_F26Dot6 charSize = (FT_F26Dot6)(roundedSize * (1 << 6));
    //error = FT_Set_Pixel_Sizes(face, charSize, charSize);
    error = FT_Set_Char_Size(face, charSize, charSize, 72, 72); // * (1 << 6) == / 64
    // ??: what's the difference: FT_Set_Pixel_Sizes vs FT_Set_Char_Size

    FT_Int32 loadFlags = GetLoadFlags(m_FontProperties.renderMode);
    FT_UInt glyphIndex = FT_Get_Char_Index(face, c);
    error = FT_Load_Glyph(face, glyphIndex, loadFlags);
    if (error != FT_Err_Ok)
    {
        /// <TODO>
        /// Load from fallback fonts - This is legacy code, fallback loading is now handled in the new LoadGlyph method
        /// </TODO>
        // Legacy fallback code removed - use new LoadGlyph method instead
        return false;
    }

    // store the glyph index
    out->glyphIndex = glyphIndex;

    // Step 3: Apply setting to face

    // Unity use Italic here by matrix, but this will change the rendered bitmap.
    // We will do this later in TextGenerator
    /* The meaning of the matrix equals to: angle = 0.
     * matrix.xx = (FT_Fixed)( cos( angle ) * 0x10000L );
     * matrix.xy = (FT_Fixed)(-sin( angle ) * 0x10000L );
     * matrix.yx = (FT_Fixed)( sin( angle ) * 0x10000L );
     * matrix.yy = (FT_Fixed)( cos( angle ) * 0x10000L ); */
    static FT_Matrix matrix
    {
        (FT_Fixed)(1 * 0x10000L), //xx
        (FT_Fixed)(0 * 0x10000L), //xy
        (FT_Fixed)(0 * 0x10000L), //yx
        (FT_Fixed)(1 * 0x10000L)  //yy
    };
    FT_Set_Transform(face, &matrix, nullptr);

    // Step 4: Render Glyph to get the bitmap

    auto slot = face->glyph;
    out->advance.x = slot->advance.x * kInvOfShiftFactor;
    out->advance.y = slot->advance.y * kInvOfShiftFactor;
    //out->ascent = slot->metrics.horiBearingY * kInvOfShiftFactor;
    /*
    out->bounds = GlyphBounds{
        slot->metrics.horiBearingX * kInvOfShiftFactor,
        slot->metrics.horiBearingY * kInvOfShiftFactor,
        slot->metrics.width * kInvOfShiftFactor,
        slot->metrics.height * kInvOfShiftFactor
    };
    */
    // skip glyph rendering and packing for white space characters
    if constexpr (renderGlyph)
    {
        PROFILER_START("Render Glyph");
        out->spreadSize = 0;
        FT_Render_Mode ftRenderMode;
        // More ditails on sdf or bsdf:
        // https://gitlab.freedesktop.org/freetype/freetype-demos/-/blob/master/src/ftsdf.c
        switch (m_FontProperties.renderMode)
        {
            case RenderMode::SDF:
            {
                int spreadSize = SDFSpreadSize(roundedSize);
                out->spreadSize = static_cast<int8>(spreadSize);
                ftRenderMode = FT_RENDER_MODE_SDF;
                // Apply sdf spread
                // TODO: Apply only once it is changed
                // Note that spread size will affect the width & height of a bitmap
                FT_Property_Set(s_FTLibrary, "sdf", "spread", &spreadSize);
                break;
            }

            case RenderMode::BSDF:
            {
                int spreadSize = SDFSpreadSize(roundedSize);
                out->spreadSize = static_cast<int8>(spreadSize);
                ftRenderMode = FT_RENDER_MODE_SDF;
                FT_Property_Set(s_FTLibrary, "bsdf", "spread", &spreadSize);
                // force use bsdf
                FT_Render_Glyph(slot, FT_RENDER_MODE_NORMAL);
                break;
            }

            default:
                ftRenderMode = FT_LOAD_TARGET_MODE(loadFlags);
                break;
        }

        error = FT_Render_Glyph(slot, ftRenderMode);
        PROFILER_END();
        if (error != FT_Err_Ok)
        {
            return false;
        }

        // bitmap convertion
        FT_Bitmap& bitmap = slot->bitmap;
        FT_Bitmap* srcBitmap = 0;
        {
            if (bitmap.pixel_mode != FT_PIXEL_MODE_GRAY)
            {
                /*
                if (!g_bitmap8bppInit)
                {
                    FT_Bitmap_New(&g_bitmap8bpp);
                    g_bitmap8bppInit = true;
                }
                FT_Bitmap_Convert(g_ftLib, &bitmap, &g_bitmap8bpp, 4);
                srcBitmap = &g_bitmap8bpp;
                if (srcBitmap->num_grays != 256)
                {
                    float factor = 1.0f / (srcBitmap->num_grays - 1) * 255;
                    for (int i = 0; i < srcBitmap->pitch * srcBitmap->rows; i++)
                        srcBitmap->buffer[i] *= factor;
                }
                */
                srcBitmap = &bitmap;
            }
            else
            {
                srcBitmap = &bitmap;
            }
        }

        out->bounds = GlyphBounds
        {
            static_cast<float>(slot->bitmap_left),
            static_cast<float>(slot->bitmap_top),
            static_cast<float>(bitmap.width),
            static_cast<float>(bitmap.rows)
        };

        // Step 5: Post Process goes here
        //if (m_PostProcessor != nullptr) m_PostProcessor->Process(srcBitmap->buffer, srcBitmap->width, srcBitmap->rows);

        // Step 6: Pack Glyph
        out->rect.width = srcBitmap->width;
        out->rect.height = srcBitmap->rows;
        if (!TryPackGlyph(c, roundedSize, srcBitmap->buffer, *out))
        {
            // output error!
            return false;
        }

        //out->rect.x += slot->bitmap_left;
        //out->rect.y += (slot->bitmap_top - srcBitmap->rows);
    }
    else
    {
        CacheGlyph(c, roundedSize, *out);
    }

    //printf("%d pack succeed!\n", c);

    return true;
}
#endif

bool UniFont::GetFontForGlyphInFallbacks(const GlyphLoadParam& inParam, UniFont** outFont)
{
    if (!m_FallbackFonts.empty())
    {
        // Try to find a fallback font that can render the glyph
        for (auto& fallbackFont : m_FallbackFonts)
        {
            if (fallbackFont->GetCharIndex(inParam.character) != 0)
            {
                *outFont = fallbackFont;
                return true;
            }
        }
    }

    // find in family fonts
    auto fontCache = UniTextGlobal::GetFontCache();
    auto fallbackManager = UniTextGlobal::GetFontFallbackCache();
    if (fontCache == nullptr || fallbackManager == nullptr)
    {
        return false;
    }

    auto familyFonts = fallbackManager->GetFamilyFonts(GetFamilyName());
    if (familyFonts.empty())
    {
        return false;
    }

    for (auto& familyFont : familyFonts)
    {
        auto font = fontCache->GetFont(familyFont.second);
        if (font != nullptr && font->GetCharIndex(inParam.character) != 0)
        {
            *outFont = font;
            return true;
        }
    }

    // find in system fonts
    ScriptType script = fallbackManager->DetectScriptType(inParam.character);
    // Get all available fallback fonts for this script from UniFontFallbackCache
    std::vector<FontFallbackInfo> fallbackInfos;
    fallbackManager->GetFallbackFonts(script, fallbackInfos);

    // There is no more recursive fallback loading here
    // if (inParam.allowSystemFallbacks)
    // {
    for (const auto& fallbackInfo : fallbackInfos)
    {
        if (!fallbackInfo.path.empty())
        {
            auto fallbackFont = fontCache->GetFont(fallbackInfo.path.c_str());
            if (fallbackFont != nullptr)
            {
                if (fallbackFont->GetCharIndex(inParam.character) != 0)
                {
                    *outFont = fallbackFont;
                    return true;
                }
            }
        }
    }
    // }

    return false;
}

template<bool renderGlyph>
bool UniFont::LoadGlyph(const GlyphLoadParam& inParam, GlyphInfo** out) 
{
    // here we rounded the incoming fontSize
    // so that we don't have to render for each fontSize
    // Update: inParam.fontSize is already rounded
    int roundedSize = inParam.fontSize; //RoundFontSize(inParam.fontSize, m_FontProperties.minFontSize, m_FontProperties.maxFontSize);

    // Step 1: Get from cache
    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable == nullptr)
    {
        return false;
    }

    if (lookUpTable->FindGlyphInfo(inParam.character, roundedSize, out))
    {
        (*out)->usedCount++;
        return true;
    }

    // Step 2: If glyph not found, Load in fallbacks
    IUniFontImpl* pImpl = GetImpl();
    if (!pImpl)
    {
        return false;
    }

    // Use renderMode from inParam if specified, otherwise use font's default renderMode
    RenderMode renderMode = (inParam.renderMode != RenderMode::NotSpecified)
                                ? inParam.renderMode
                                : m_FontProperties.renderMode;

    auto glyphIndex = pImpl->GetCharIndex(inParam.character);
    if (glyphIndex == 0)
    {
        UniFont* fallbackFont = nullptr;
        if (!GetFontForGlyphInFallbacks(inParam, &fallbackFont))
        {
            return false;
        }

        pImpl = fallbackFont->GetImpl();
        if (!pImpl)
        {
            return false;
        }
        glyphIndex = pImpl->GetCharIndex(inParam.character);
    }

    GlyphInfo info;
    // store the glyph index
    info.glyphIndex = glyphIndex;
    //info->unicode = inParam.character;
    GlyphInfo* pInfo = &info;

    // Step 3: Load the glyph but DO NOT Render
    if (!pImpl->LoadGlyph(glyphIndex, roundedSize, renderMode, &pInfo, 0))
    {
        return false;
    }

    // renderGlyph is for skipping white space characters
    if constexpr (renderGlyph)
    {
        PROFILER_START("Render Glyph");

        decltype(auto) textureEntry = TryPackGlyph(inParam.character, roundedSize, info);
        if (textureEntry != nullptr)
        {
            *out = lookUpTable->EmplaceGlyph(inParam.character, roundedSize, std::move(info));
            // pack first then render
            return pImpl->RenderGlyph(GlyphRenderData {
                (uint16)roundedSize,
                renderMode,
                pInfo,
                textureEntry
            });
        }

        // output error!
        PROFILER_END();
        return false;
    }
    else
    {
        *out = lookUpTable->EmplaceGlyph(inParam.character, roundedSize, std::move(info));
        CacheGlyph(inParam.character, roundedSize, * *out);
    }

    return true;
}

/*
bool UniFont::LoadGlyphInternally(const GlyphLoadParam& inParam, GlyphInfo** out)
{
    if (m_pImpl == nullptr)
    {
        return false;
    }

    return m_pImpl->LoadGlyph(inParam.character, inParam.fontSize, inParam.renderMode, out, 0);
}
*/

#if 0
// Old code as a reminder how this project envolves.
template<bool renderGlyph>
bool UniFont::LoadGlyph(const uint16 c, int fontSize, GlyphInfo** out)
{
    return LoadGlyphNew<renderGlyph>(c, fontSize, out);

#if 0
    //bool isGlyphCached = false;
    int roundedSize = RoundFontSize(fontSize, m_FontProperties.minFontSize, m_FontProperties.maxFontSize);

    // Step 1: Get from cache
    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable == nullptr)
    {
        return false;
    }

    if (lookUpTable->FindGlyphInfo(c, out))
    {
        (*out)->usedCount++;
        return true;
    }

    /*
    auto entry = GlyphEntry(roundedSize, c);
    if (m_GlyphLookupMap.size() > 0)
    {
        auto entry_it = m_GlyphLookupMap.find(entry);
        if (entry_it != m_GlyphLookupMap.end())
        {
            decltype(auto) page = m_AtlasEntry->GetGlyphPage(roundedSize, entry_it->second);
            if (page != nullptr)
            {
                // find and size did not change
                decltype(auto) ret = page->glyphs->find(c);
                if (ret != page->glyphs->end())
                {
                    ret->second.usedCount++;
                    *out = ret->second;
                    return true;
                }
                else
                {
                    m_GlyphLookupMap.erase(entry_it);
                }
            }
        }
    }
    */

    // Step 2: If glyph not found, Load in fallbacks
    auto glyphIndex = m_FontImpl.GetCharIndex(c);
    if (glyphIndex == 0)
    {
        return LoadGlyphInFallbacks(c, fontSize);
    }

    GlyphInfo info;
    GlyphInfo* pInfo = &info;

    // *out = &lookUpTable->map.emplace(c, GlyphInfo{}).first->second;

    //info->unicode = c;
    // store the glyph index
    info.glyphIndex = glyphIndex;

    // Step 3: Load the glyph but DO NOT Render
    if (!m_FontImpl.LoadGlyph(glyphIndex, roundedSize, m_FontProperties.renderMode, &pInfo))
    {
        return false;
    }

    // skip glyph rendering and packing for white space characters
    if constexpr (renderGlyph)
    {
        PROFILER_START("Render Glyph");
        uint8* buffer = nullptr;
        // @TODO:
        // Render & Pack can be parallel !
        // Step 5: Render and Pack the glyph
        if (m_FontImpl.RenderGlyph(glyphIndex, roundedSize, m_FontProperties.renderMode, &pInfo, &buffer))
        {
            short pageIndex;
            if (TryPackGlyph(c, roundedSize, buffer, *pInfo, pageIndex))
            {
                //m_GlyphLookupMap[entry] = { pageIndex };
                *out = &lookUpTable->map.emplace(c, info).first->second;
                return true;
            }
        }

        // output error!
        PROFILER_END();
        return false;
    }
    else
    {
        *out = &lookUpTable->map.emplace(c, info).first->second;
        CacheGlyph(c, roundedSize, **out);
        //m_GlyphLookupMap[entry] = { 0 };
    }

    return true;

#endif

}

#endif

#if USE_THREAD_POOL
void UniFont::LoadGlyphAsync(const uint16 c, int fontSize, GlyphInfo** out)
{
    int roundedSize = RoundFontSize(fontSize, m_FontProperties.minFontSize, m_FontProperties.maxFontSize);

    // Step 1: Get from cache
    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable != nullptr)
    {
        if (lookUpTable->FindGlyphInfo(c, out))
        {
            return;
        }
    }

    // record first: avoid redundant tasks
    //m_GlyphLookupMap[entry] = { -1 };
    *out = &lookUpTable->map.emplace(c, GlyphInfo{}).first->second;

    jobDataList.emplace_back(JobData
        {
            c, roundedSize,
            *out
        });

    s_ThreadPool.push_task(&UniFont::LoadGlyphAsyncInternal, this, jobDataList.size() - 1);
}


void UniFont::LoadGlyphAsyncInternal(size_t jobDataIndex, unsigned int thread_id)
{
    //uint32 thread_id = jobDataIndex % kThreadNum;
    //printf("Job Executing : %zu, thread_id = %u\n", jobDataIndex, thread_id);

    //const std::scoped_lock total_lock_local(s_Mutex[thread_id]);

    auto jobData = jobDataList[jobDataIndex];
    // Step 2: If glyph not found, Load in fallbacks
    auto glyphIndex = m_FontImpl.GetCharIndex(jobData.unicode, thread_id);
    if (glyphIndex == 0)
    {
        return;
    }

    // store the glyph index
    jobData.out->glyphIndex = glyphIndex;

    // Load the glyph but DO NOT Render
    if (m_FontImpl.LoadGlyph(jobData.out->glyphIndex, jobData.fontSize, m_FontProperties.renderMode, &jobData.out, thread_id))
    {
        uint8* buffer;
        if (m_FontImpl.RenderGlyph(jobData.out->glyphIndex, jobData.fontSize, m_FontProperties.renderMode, &jobData.out, &buffer, thread_id))
        {
            const std::scoped_lock g_lock(g_Mutex);
            // 这种方式来pack的，需要先--，否则后续会再加一次，计数就错了
            jobData.out->usedCount--;
            short pageIndex;
            if (TryPackGlyph(jobData.unicode, jobData.fontSize, buffer, *jobData.out, pageIndex))
            {
                //m_GlyphLookupMap[GlyphEntry(jobData.fontSize, jobData.unicode)] = pageIndex;
                return;
            }
        }
    }
}

void UniFont::FinishAsyncJobs()
{
    s_ThreadPool.wait_for_tasks();
    //for (auto& data : jobDataList)
    //{
    //}
    jobDataList.clear();
}
#endif

// Explicit template instantiation: fix linking issue
template bool UniFont::LoadGlyph<true>(const GlyphLoadParam& inParam, GlyphInfo** out);
template bool UniFont::LoadGlyph<false>(const GlyphLoadParam& inParam, GlyphInfo** out);

void UniFont::UnloadGlyph(const uint16 c, int fontSize)
{
    auto roundedSize = RoundFontSize(fontSize, m_FontProperties.minFontSize, m_FontProperties.maxFontSize);
    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(fontSize);
    if (lookUpTable != nullptr)
    {
        GlyphInfo* glyph;
        if (lookUpTable->FindGlyphInfo(c, roundedSize, &glyph))
        {
            --glyph->usedCount;
        }
    }
}

int UniFont::UnloadUnusedGlyphs()
{
    // NOTE:
    // m_GlyphLookupMap didn't gets freed... 
    // right now no bug happend because we performed a double search.
    // See more in LoadGlyph
    // m_GlyphLookupMap.clear();
    m_AtlasEntry->FreeUnusedPages();
    return 0;
}

void UniFont::ClearGlyphs()
{
    //m_GlyphLookupMap.clear();
    m_AtlasEntry->Clear();
}

//////////////////////////////////////////////
/// privates
//////////////////////////////////////////////
/*
bool UniFont::LoadGlyphInFallbacks(const GlyphLoadParam& inParam, GlyphInfo** out)
{
    int currentFontGUID = GetFontGUID();

    // user-specified fallback fonts
    if (m_FallbackFonts.size() > 0)
    {
        for (auto fallbackFont : m_FallbackFonts)
        {
            if (fallbackFont != nullptr)
            {
                // Skip if this fallback font is the same as current font
                if (fallbackFont->GetFontGUID() == currentFontGUID)
                {
                    continue;
                }

                // Load glyph from fallback font but cache it in this font
                GlyphLoadParam fallbackParam = inParam;
                fallbackParam.allowSystemFallbacks = false;
                if (fallbackFont->LoadGlyphInternally(fallbackParam, out))
                {
                    // Successfully loaded from a user-specified fallback font
                    return true;
                }
            }
        }
    }

    // Try family fallbacks (fonts in the same family with different styles)
    auto fallbackManager = UniTextGlobal::GetFontFallbackCache();
    auto fontCache = UniTextGlobal::GetFontCache();

    if (!fallbackManager || !fontCache)
    {
        return false;
    }

    // Try family fallbacks (fonts in the same family with different styles)
    const std::string& familyName = GetFamilyName();
    if (!familyName.empty())
    {
        const auto& familyFonts = fallbackManager->GetFamilyFonts(familyName);

        // Try other styles in the same family
        for (const auto& stylePair : familyFonts)
        {
            if (stylePair.second != currentFontGUID) // Skip current font
            {
                auto fallbackFont = fontCache->GetFont(stylePair.second);
                if (fallbackFont)
                {
                    // For family fallbacks, load glyph from fallback font but cache it in this font
                    GlyphLoadParam familyParam = inParam;
                    familyParam.allowSystemFallbacks = false;
                    if (fallbackFont->LoadGlyphInternally(familyParam, out))
                    {
                        return true;
                    }
                }
            }
        }
    }

    // If not found in family fallbacks, try system fallbacks

    // Detect the script type for the character
    ScriptType script = fallbackManager->DetectScriptType(inParam.character);
    // Get all available fallback fonts for this script from UniFontFallbackCache
    std::vector<FontFallbackInfo> fallbackInfos;
    fallbackManager->GetFallbackFonts(script, fallbackInfos);

    // Only try system fallbacks if we allow them (prevents infinite recursion)
    if (inParam.allowSystemFallbacks)
    {
        for (const auto& fallbackInfo : fallbackInfos)
        {
            if (!fallbackInfo.path.empty())
            {
                auto fallbackFont = fontCache->GetFont(fallbackInfo.path.c_str());
                if (fallbackFont != nullptr)
                {
                    // Skip if this is the same font as current font
                    if (fallbackFont->GetFontGUID() == currentFontGUID)
                    {
                        continue;
                    }

                    // Load glyph from system fallback font but cache it in this font
                    GlyphLoadParam systemParam = inParam;
                    systemParam.allowSystemFallbacks = false;
                    if (fallbackFont->LoadGlyphInternally(systemParam, out))
                    {
                        return true;
                    }
                }
            }
        }
    }

    // No fallback found
    return false;
}
*/

#if USE_LINEAR_PACK
// Linear search
bool UniFont::TryPackGlyph(const uint16 c, const int roundedSize, const unsigned char* bitmap,
                           GlyphInfo& out, short& pageIndex) {
    PROFILER_START("Pack Glyph");

    int requiredFontSize = roundedSize + out.spreadSize;
    int requiredPageSize = FontSizeToPageSize(requiredFontSize);

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable == nullptr) return false;

    auto atlasManager = UniTextGlobal::GetAtlasCache();
    auto& pageList = lookUpTable->pageList;
    //int roundedSize = RoundToFontSize(desiredFontSize);
    //pageList = m_AtlasEntry->GetGlyphPageList(roundedSize);
    if (pageList.size() == 0)
    {
        TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
        if (atlasManager->FindNewEntry(entry))
        {
            pageList.emplace_back(SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
        }
        //SizedPage new_page{};
        //if (new_page.Initialize(requiredFontSize, requiredPageSize, requiredPageSize, ITexture::Format::A8,
        //    m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation))
        //{
        //    pageList->emplace_back(std::move(new_page));
        //}
    }

    bool isPacked = false;
    pageIndex = 0;
    while (pageIndex < pageList.size())
    {
        if (pageList[pageIndex].glyphPacker == nullptr)
        {
            // page has been released
            TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
            if (atlasManager->FindNewEntry(entry))
            {
                // move automatically
                pageList[pageIndex] = SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation);
            }
        }

        auto& page = pageList[pageIndex];
        if (page.glyphPacker->Pack(&out.rect))
        {
            // Step 7: Add data to Texture
            isPacked = true;
            page.textureEntry.WriteData(out.rect, bitmap);
            out.rect.x += page.textureEntry.textureRect.x;
            out.rect.y += page.textureEntry.textureRect.y;
            out.usedCount++;
            //page.glyphs->insert(std::make_pair(c, out));
            //lookUpTable->map[c] = GlyphIndexer{ pageIndex, static_cast<short>(page.glyphs->size()) };
            //page.glyphs->emplace_back(out);
            out.pageIndex = pageIndex;
            page.glyphCount++;
            break;
        }

        //if (isPacked) break;

        // we have reached the last page and still no good
        if (pageIndex == pageList.size() - 1)
        {
            // in the worst scenario, we are looping over too many pages to find the one that could fit.
            //printf("Trying to find a new page: %d, at size %d\n", pageIndex, requiredPageSize);
            TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
            if (atlasManager->FindNewEntry(entry))
            {
                pageList.emplace_back(SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
            }

            //SizedPage new_page{};
            //if (new_page.Initialize(requiredFontSize, requiredPageSize, requiredPageSize, ITexture::Format::A8,
            //    m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation))
            //{
            //    pageList->emplace_back(std::move(new_page));
            //}
        }

        ++pageIndex;
    }

    PROFILER_END();
    return isPacked;
}
#else

// Queue based search
bool UniFont::TryPackGlyph(const uint16 c, const int roundedSize, const unsigned char* bitmap,
                           GlyphInfo& out, short& pageIndex) 
{
    PROFILER_START("Pack Glyph");

    int requiredFontSize = roundedSize + out.spreadSize;
    int requiredPageSize = ::FontSizeToPageSize(requiredFontSize);

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable == nullptr) return false;

    auto atlasManager = UniTextGlobal::GetAtlasCache();
    auto& pageList = lookUpTable->pageList;
    auto& pageSearchIndex = lookUpTable->searchPageList;
    if (pageList.size() == 0)
    {
        TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
        if (atlasManager->FindNewEntry(entry))
        {
            pageSearchIndex.push(pageList.size());
            pageList.emplace_back(SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
        }
    }

    bool isPacked = false;
    int availableSize = pageSearchIndex.size();
    while (availableSize > 0)
    {
        pageIndex = pageSearchIndex.front();
        if (pageList[pageIndex].glyphPacker == nullptr)
        {
            // page has been released
            TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
            if (atlasManager->FindNewEntry(entry))
            {
                // move automatically
                pageList[pageIndex] = SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation);
            }
        }

        auto& page = pageList[pageIndex];
        if (page.glyphPacker->Pack(&out.rect))
        {
            // Step 7: Add data to Texture
            isPacked = true;
            // @TODO:
            // move WriteData into a seperate method for parallel
            page.textureEntry.WriteData(out.rect, bitmap);
            // don't add rect.x and rect.y here anymore.
            // out.rect.x += page.textureEntry.textureRect.x;
            // out.rect.y += page.textureEntry.textureRect.y;
            out.usedCount++;
            out.pageIndex = pageIndex;
            page.glyphCount++;
            break;
        }

        pageSearchIndex.pop();
        if (pageSearchIndex.size() == 0)
        {
            // try add a new page
            TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
            if (atlasManager->FindNewEntry(entry))
            {
                pageSearchIndex.push(pageList.size());
                pageList.emplace_back(SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
            }
        }

        availableSize = pageSearchIndex.size();
    }

    //if (!isPacked)
    //    printf("glyph = %c, packed ret = %s\n", c, isPacked ? "true" : "false");

    PROFILER_END();
    return isPacked;
}

TextureEntry* UniFont::TryPackGlyph(const uint16 c, const int roundedSize, GlyphInfo& out) 
{
    PROFILER_START("Pack Glyph");

    // 这里原本用到了spreadSize，整体设计不太一致
    int requiredFontSize = roundedSize;// + out.spreadSize;
    int requiredPageSize = FontSizeToPageSize(requiredFontSize);

    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable == nullptr) return nullptr;

    auto atlasManager = UniTextGlobal::GetAtlasCache();
    auto& pageList = lookUpTable->pageList;
    auto& pageSearchIndex = lookUpTable->searchPageList;
    if (pageList.size() == 0)
    {
        TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
        if (atlasManager->FindNewEntry(entry))
        {
            pageSearchIndex.push(pageList.size());
            pageList.emplace_back(SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
        }
    }

    TextureEntry* entry = nullptr;
    int availableSize = pageSearchIndex.size();
    while (availableSize > 0)
    {
        auto pageIndex = pageSearchIndex.front();
        if (pageList[pageIndex].glyphPacker == nullptr)
        {
            // page has been released
            TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
            if (atlasManager->FindNewEntry(entry))
            {
                // move automatically
                pageList[pageIndex] = SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation);
            }
        }

        auto& page = pageList[pageIndex];
        if (page.glyphPacker->Pack(&out.rect))
        {
            // Step 7: Add data to Texture
            // out.rect.x += page.textureEntry.textureRect.x;
            // out.rect.y += page.textureEntry.textureRect.y;
            out.usedCount++;
            out.pageIndex = pageIndex;
            entry = &page.textureEntry;
            page.glyphCount++;
            break;
        }

        pageSearchIndex.pop();
        if (pageSearchIndex.size() == 0)
        {
            // try add a new page
            TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
            if (atlasManager->FindNewEntry(entry))
            {
                pageSearchIndex.push(pageList.size());
                pageList.emplace_back(SizedPage(requiredFontSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
            }
        }

        availableSize = pageSearchIndex.size();
    }

    PROFILER_END();
    return entry;
}

// bool UniFont::UploadToAtlas(const int roundedSize, const short pageIndex, const GlyphRect& packedRect, const unsigned char* bitmap)
// {
//     auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
//     if (lookUpTable == nullptr) return false;

//     auto& pageList = lookUpTable->pageList;
//     if (pageIndex < 0 || pageIndex >= pageList.size())
//     {
//         return false;
//     }

//     auto& page = pageList[pageIndex];
//     if (page.textureEntry.textureIndex >= 0)
//     {
//         page.textureEntry.WriteData(packedRect, bitmap);
//     }

//     return true;
// }
#endif

void UniFont::CacheGlyph(const uint16 c, const int roundedSize, GlyphInfo& out)
{
    out.pageIndex = 0;
    out.usedCount++;
    /*
    auto lookUpTable = m_AtlasEntry->GetGlyphLookUpTable(roundedSize);
    if (lookUpTable == nullptr) return;

    auto& pageList = lookUpTable->pageList;
    if (pageList.size() == 0)
    {
        int requiredPageSize = FontSizeToPageSize(roundedSize);

        TextureEntry entry{ ITexture::Format::A8, -1, GlyphRect(0, 0, requiredPageSize, requiredPageSize) };
        auto* atlasManager = UniTextGlobal::GetAtlasCache();
        if (atlasManager->FindNewEntry(entry))
        {
            pageList.emplace_back(SizedPage(roundedSize, entry, m_FontProperties.packingMethod, m_FontProperties.padding, m_FontProperties.allowRotation));
        }
    }

    out.usedCount++;

    for (auto& page : pageList)
    {
        //page.glyphs->insert(std::make_pair(c, out));
        //lookUpTable->map[c] = GlyphIndexer{ 0, static_cast<short>(page.glyphs->size()) };
        //page.glyphs->emplace_back(out);
        out.pageIndex = 0;
        page.glyphCount++;
        break;
    }
    */
}


//////////////////////////////////////////////
/// UniFont Caching
//////////////////////////////////////////////
/*
UniFont* UniFontCache::GetFont(const std::string_view& fontName)
{
    auto it = m_FontCache.find(fontName);
    if (it != m_FontCache.end())
    {
        return it->second;
    }

    auto font = UniFont::CreateFromFile(fontName);
    m_FontCache[fontName] = font;

    return font;
}
*/

template<bool performLoad>
UniFont* UniFontCache::GetFont(const char* fontPath)
{
    if (fontPath == nullptr) return nullptr;

    // First check if we already have a GUID for this path
    auto pathIt = m_PathToGuidMap.find(fontPath);
    if (pathIt != m_PathToGuidMap.end())
    {
        // We have the GUID, now get the font
        auto fontIt = m_FontCache.find(pathIt->second);
        if (fontIt != m_FontCache.end())
        {
            return fontIt->second.get();
        }
        // If font not found but GUID exists, remove stale GUID mapping
        m_PathToGuidMap.erase(pathIt);
    }

    if constexpr (performLoad)
    {
        auto font = UniFont::CreateFromFile(fontPath);
        if (font)
        {
            auto font_ptr = font.get();
            int fontGuid = font->GetFontGUID();

            // Store in both maps
            m_FontCache[fontGuid] = std::move(font);
            m_PathToGuidMap[fontPath] = fontGuid;

            // Automatically register font to family
            auto fallbackCache = UniTextGlobal::GetFontFallbackCache();
            if (fallbackCache)
            {
                const std::string& familyName = font_ptr->GetFamilyName();
                FontStyle fontStyle = font_ptr->GetFontStyle();
                if (!familyName.empty())
                {
                    fallbackCache->RegisterFontToFamily(familyName, fontStyle, fontGuid);
                }
            }

            return font_ptr;
        }
    }

    return nullptr;
}

// Note: Explicitly instantiate templates to avoid link error
/// <summary>
/// GetFont<true> is used internally
/// </summary>
template UniFont* UniFontCache::GetFont<true>(const char* fontPath);

/// <summary>
/// GetFont<false> is used for Unity
/// </summary>
template UniFont* UniFontCache::GetFont<false>(const char* fontPath);

UniFont* UniFontCache::GetFont(int fontGuid)
{
    auto it = m_FontCache.find(fontGuid);
    if (it != m_FontCache.end())
    {
        return it->second.get();
    }

    return nullptr;
}

UniFont* UniFontCache::LoadFontFromMemory(const char* fontName, const unsigned char* fontBuffer, unsigned long bufferSize)
{
    if (fontName == nullptr) return nullptr;

    // Check if we already have a GUID for this font name
    auto pathIt = m_PathToGuidMap.find(fontName);
    if (pathIt != m_PathToGuidMap.end())
    {
        // We have the GUID, now get the font
        auto fontIt = m_FontCache.find(pathIt->second);
        if (fontIt != m_FontCache.end())
        {
            return fontIt->second.get();
        }
        // If font not found but GUID exists, remove stale GUID mapping
        m_PathToGuidMap.erase(pathIt);
    }

    auto font = UniFont::CreateFromMemory(fontName, fontBuffer, bufferSize);
    if (font)
    {
        auto font_ptr = font.get();
        int fontGuid = font->GetFontGUID();

        // Store in both maps
        m_FontCache[fontGuid] = std::move(font);
        m_PathToGuidMap[fontName] = fontGuid;

        // Automatically register font to family
        auto fallbackCache = UniTextGlobal::GetFontFallbackCache();
        if (fallbackCache)
        {
            const std::string& familyName = font_ptr->GetFamilyName();
            FontStyle fontStyle = font_ptr->GetFontStyle();
            if (!familyName.empty())
            {
                fallbackCache->RegisterFontToFamily(familyName, fontStyle, fontGuid);
            }
        }

        return font_ptr;
    }

    return nullptr;
}

#if USE_HARFBUZZ
bool UniFont::GetFontData(const unsigned char** outData, unsigned long* outSize) const
{
    if (m_pImpl)
    {
        return m_pImpl->GetFontData(outData, outSize);
    }
    return false;
}

void* UniFont::GetFreeTypeFace() const
{
    if (m_pImpl)
    {
        return m_pImpl->GetFreeTypeFace();
    }
    return nullptr;
}
#endif
